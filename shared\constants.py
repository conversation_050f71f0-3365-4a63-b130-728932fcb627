# coding: utf-8

# -------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
# --------------------------------------------------------------------------

"""
FILE: constants.py

DESCRIPTION:
    This module defines constants required to run the sample codes.
"""

CONFIGURATION_NAME_FACE_API_ENDPOINT = "AZURE_FACE_API_ENDPOINT"
CONFIGURATION_NAME_FACE_API_ACCOUNT_KEY = "AZURE_FACE_API_ACCOUNT_KEY"

DEFAULT_FACE_API_ENDPOINT = ""
DEFAULT_FACE_API_ACCOUNT_KEY = ""


# Test images
class TestImages(object):
    IMAGE_PARENT_FOLDER = "../images/"
    IMAGE_FAMILY_1_DAD_1 = "Family1-Dad1.jpg"
    IMAGE_FAMILY_1_DAD_2 = "Family1-Dad2.jpg"
    IMAGE_FAMILY_1_DAD_3 = "Family1-Dad3.jpg"
    IMAGE_FAMILY_1_DAUGHTER_1 = "Family1-Daughter1.jpg"
    IMAGE_FAMILY_1_DAUGHTER_2 = "Family1-Daughter2.jpg"
    IMAGE_FAMILY_1_DAUGHTER_3 = "Family1-Daughter3.jpg"
    IMAGE_FAMILY_1_MOM_1 = "Family1-Mom1.jpg"
    IMAGE_FAMILY_1_MOM_2 = "Family1-Mom2.jpg"
    IMAGE_FAMILY_1_SON_1 = "Family1-Son1.jpg"
    IMAGE_FAMILY_1_SON_2 = "Family1-Son2.jpg"
    IMAGE_FAMILY_2_LADY_1 = "Family2-Lady1.jpg"
    IMAGE_FAMILY_2_LADY_2 = "Family2-Lady2.jpg"
    IMAGE_FAMILY_2_Man_1 = "Family2-Man1.jpg"
    IMAGE_FAMILY_2_Man_2 = "Family2-Man2.jpg"
    IMAGE_FAMILY_3_LADY_1 = "Family3-Lady1.jpg"
    IMAGE_FAMILY_3_Man_1 = "Family3-Man1.jpg"
    IMAGE_CHILD_1_PERSON_GROUP = "child1-person-group.jpg"
    IMAGE_CHILD_2_PERSON_GROUP = "child2-person-group.jpg"
    IMAGE_CHILD_3_PERSON_GROUP = "child3-person-group.jpg"
    IMAGE_DETECTION_1 = "detection1.jpg"
    IMAGE_DETECTION_2 = "detection2.jpg"
    IMAGE_DETECTION_3 = "detection3.jpg"
    IMAGE_DETECTION_4 = "detection4.jpg"
    IMAGE_DETECTION_5 = "detection5.jpg"
    IMAGE_DETECTION_6 = "detection6.jpg"
    IMAGE_EXTRA_WOMAN_IMAGE = "extra-woman-image.jpg"
    IMAGE_FINDSIMILAR = "findsimilar.jpg"
    IMAGE_IDENTIFICATION1 = "identification1.jpg"
    IMAGE_MAN1_PERSON_GROUP = "man1-person-group.jpg"
    IMAGE_MAN2_PERSON_GROUP = "man2-person-group.jpg"
    IMAGE_MAN3_PERSON_GROUP = "man3-person-group.jpg"
    TEST_IMAGE_PERSON_GROUP = "test-image-person-group.jpg"
    IMAGE_WOMAN1_PERSON_GROUP = "woman1-person-group.jpg"
    IMAGE_WOMAN2_PERSON_GROUP = "woman2-person-group.jpg"
    IMAGE_WOMAN3_PERSON_GROUP = "woman3-person-group.jpg"
    IMAGE_NINE_FACES = "nine-faces.png"

    DEFAULT_IMAGE_URL = "https://aka.ms/facesampleurl"
    DEFAULT_IMAGE_FILE = IMAGE_DETECTION_1
